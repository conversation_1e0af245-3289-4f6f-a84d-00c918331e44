// API Response Types
export interface ScoreCardData {
  Collect_recovery_ratio: number;
  outstanding_tickets: number;
  'total_tickets_<_5%': number;
  vcnt_units: number;
  avg_vcnt_days: number;
  aged_vacant_units: number;
  Occupancy_Non_Rev: number;
  Occupancy_Trend: number;
  gain_loss: number;
  units_available: number;
  t30_show: number;
  In_Place_rent: number;
  'In_Place_rent/sqft': number;
  Prev_Yr_In_Place_rent: number;
  New_In_Place_rent: number;
  'New_In_Place_rent/sqft': number;
  Prev_Yr_New_In_Place_rent: number;
  Renewal_In_Place_rent: number;
  'Renewal_In_Place_rent/sqft': number;
  Prev_Yr_Renewal_In_Place_rent: number;
  MTM: number;
  Income: number;
  Controllable_Opex: number;
  total_OPex: number;
  NOI: number;
  Controll_NOI: number;
  Capital: number;
  COST_Per_Turn: number;
  collection_MTD: number;
  capital_execution: number;
  avg_turn_time: number;
  repeat_tickets: number;
  Residential_sqft: number;
  AVG_Residential_sqft: number;
  Retail_sqft: number;
  Retail_Spaces: number;
  Affordable: number;
  Non_Revenue: number;
  Down: number;
  Occupancy_Trendt30: number;
  YTD_Renewal_Conversion: number;
  'bad_debt_w/o_%_GRI': number;
  Adjusted_Period_Start_Month: number;
  Adjusted_Period_Start_Day: number;
  Adjusted_Period_Start_Year: number;
  Adjusted_Period_End_Month: number;
  Adjusted_Period_End_Day: number;
  Adjusted_Period_End_Year: number;
  Rental_Income: number;
}

export interface ScoreCardResponse {
  data: ScoreCardData;
}

export interface ScoreCardOccupancyData {
  vcnt_units: number;
  avg_vcnt_days: number;
  aged_vacant_units: number;
  Occupancy_Non_Rev: number;
  Occupancy_Trend: number;
  gain_loss: number;
  units_available: number;
  t30_show: number;
  Occupancy_Trendt30: number;
  ColorcodingOfT30: number;
}

export interface ScoreCardOccupancyResponse {
  data: ScoreCardOccupancyData;
}

export interface ScoreCardRentData {
  In_Place_rent: number;
  'In_Place_rent/sqft': number;
  Prev_Yr_In_Place_rent: number;
  New_In_Place_rent: number;
  'New_In_Place_rent/sqft': number;
  Prev_Yr_New_In_Place_rent: number;
  Renewal_In_Place_rent: number;
  'Renewal_In_Place_rent/sqft': number;
  Prev_Yr_Renewal_In_Place_rent: number;
  MTM: number;
  YTD_Renewal_Conversion: number;
}

export interface ScoreCardRentResponse {
  data: ScoreCardRentData;
}

export interface ScoreCardFinancialData {
  Income: number;
  Controllable_Opex: number;
  total_OPex: number;
  NOI: number;
  Controll_NOI: number;
  Capital: number;
  COST_Per_Turn: number;
  Rental_Income: number;
}

export interface ScoreCardFinancialResponse {
  data: ScoreCardFinancialData;
}

export interface ScoreCardOperationsData {
  outstanding_tickets: number;
  'total_tickets_<_5%': number;
  collection_MTD: number;
  capital_execution: number;
  avg_turn_time: number;
  repeat_tickets: number;
  Residential_sqft: number;
  AVG_Residential_sqft: number;
  Retail_sqft: number;
  Retail_Spaces: number;
  Affordable: number;
  Non_Revenue: number;
  Down: number;
  Adjusted_Period_Start_Month: number;
  Adjusted_Period_Start_Day: number;
  Adjusted_Period_Start_Year: number;
  Adjusted_Period_End_Month: number;
  Adjusted_Period_End_Day: number;
  Adjusted_Period_End_Year: number;
  Collect_recovery_ratio: number;
  'bad_debt_w/o_%_GRI': number;
}

export interface ScoreCardOperationsResponse {
  data: ScoreCardOperationsData;
}

export interface PropertyStrategyData {
  Property_Strategy_This_Year: string;
  Same_Store: string;
}

export interface PropertyStrategyResponse {
  data: PropertyStrategyData[];
}

export interface ImageData {
  BU: string;
  Property: string;
  name: string;
  placeid: string;
  image_url: string;
}

export interface ImagesResponse {
  data: ImageData[];
}

export interface JTurnerData {
  client_id: string;
  property_name: string;
  ora_score: string;
  national_ora_score: string;
  company_ora_score: string;
}

export interface JTurnerResponse {
  data: JTurnerData[];
}

export interface GoogleReviewData {
  propertyBu: string;
  title: string;
  placeid: string;
  rating: string;
  addressLines: string;
}

export interface GoogleReviewsResponse {
  data: GoogleReviewData[];
}

export interface PropertyData {
  property_sk: number;
  Property_Hmy: string;
  BU: string;
  Property: string;
  Region: string;
  Address: string;
  City: string;
  State: string;
  ZipCode: string;
  Market: string;
  RVP: string;
  VP: string;
  RPM: string;
  RegionalMarketingDirector: string;
  Owner: string;
  SecondaryOwner: string;
  Accountant: string;
  BeginningOfOperations: string;
  EndOfOperations: string | null;
  GoLive: string;
  Age: string;
  Class: string;
  AssetType: string;
  UnitCount: string;
  PropertyType: string;
  google_place_id: string;
  google_id: string;
  google_name: string;
  google_address: string;
  census_market: string;
  census_property_id: string;
  census_submarket: string;
  msa_id: string;
  msa_property_id: string;
  msa_property_name: string;
  msa_zip: string;
  start_date: string;
  end_date: string | null;
  is_active: boolean;
}

export interface SystemData {
  System: string;
  property_hmy: string;
  BU: string;
  PropertyName: string;
}

export interface DateData {
  year: number;
  month: number;
  day_name: string;
  month_name: string;
  date: string;
}

export interface FilterOptionsData {
  properties: PropertyData[];
  dates: DateData[];
  systems: SystemData[];
}

export interface FilterOptionsResponse {
  data: FilterOptionsData;
}

export interface SubmarketOccupancyData {
  submarket_occupancy: number;
}

export interface SubmarketOccupancyResponse {
  data: SubmarketOccupancyData[];
}
