import {
  StaffingMarketKPITypes,
  StaffingRegionalKPITypes,
  StaffingRPMKPITypes,
} from '@/api/staffingKpi/staffingKpiApi.types';
import {
  alignmentCenter,
  alignmentLeft,
  alignmentRight,
  bodyRowCellFill,
  bodyRowCellFullBorders,
  columnHeadingFillFont,
  companyNameRowFont,
  excelLightBlueColor,
  excelPurpleColor,
  filterConsolidationRowFont,
  filterPeriodRowFont,
  headingFill,
  headingFillMergeFont,
  mainHeadingCellBorders,
  reportTitleRowFont,
  rowColumnHeadingHeight,
} from '@/constants/exportExcelStyles';
import { getConsolidationHeader } from '@/helpers/exportExcelGetConsolidationHeader';
import { ReportFilters } from '@/slice/incomeReportSlice';
import ExcelJS from 'exceljs';
import { tableRowOrderFormateStaffingRegional } from './helperStaffingKPI';

export const downloadExcelStaffingPropertyCount = async (
  rpmData: StaffingRPMKPITypes[],
  filters: ReportFilters,
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Property Count ');

  let displayMonth = '';
  const currentYear = filters?.year || '2025';

  if (filters?.month?.length) {
    if (filters.month?.length === 1) {
      displayMonth = filters?.month[0];
    } else {
      displayMonth =
        filters?.month[0] + ' - ' + filters?.month[filters?.month?.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyNameRowFont;
  worksheet.mergeCells('A1:F1');

  const titleRow = worksheet.addRow([
    `Property Count and '25 Expected Annual PM Fees per RPM`,
  ]);
  titleRow.font = reportTitleRowFont;
  worksheet.mergeCells('A2:F2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);
  periodRow.font = filterPeriodRowFont;
  worksheet.mergeCells('A3:F3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = filterConsolidationRowFont;
  worksheet.mergeCells('A4:F4');

  worksheet.addRow('');
  worksheet.addRow('');

  const subHeadingsRow = [
    'RPM Name ',
    'Stabilized',
    'Lease-Up',
    'Total Properties',
    'Expected PM Fees',
  ];
  // worksheet.columns = columsWidth;
  const subheadings = worksheet.addRow(subHeadingsRow);

  subheadings.height = rowColumnHeadingHeight;

  worksheet.columns = [
    { width: 16 },
    { width: 16 },
    { width: 16 },
    { width: 16 },
    { width: 16 },
  ];

  subheadings.eachCell((cell) => {
    cell.fill = headingFill;
    cell.font = columnHeadingFillFont;
    cell.border = mainHeadingCellBorders;
    cell.alignment = alignmentCenter;
  });

  rpmData.forEach(
    (
      { Expected_PM_Fees, Lease_Up, RPM, Stabilised, Total_Properties },
      index,
    ) => {
      const rowCells = worksheet.addRow([
        RPM,
        Stabilised,
        Lease_Up,
        Total_Properties,
        Expected_PM_Fees,
      ]);

      rowCells.eachCell((cell, colNum) => {
        if (colNum !== 1) {
          cell.alignment = alignmentRight;
        } else {
          cell.alignment = alignmentLeft;
        }

        if (index % 2 === 0) {
          cell.fill = bodyRowCellFill;
          cell.border = bodyRowCellFullBorders;
        }
        if (colNum === 5) {
          //        if (unit === '$') cell.numFmt = '"$"#,##0;("$"#,##0)';
          // else if (unit === '%') cell.numFmt = '0%';
          cell.numFmt = '#,##0;(#,##0)';
        }
      });
    },
  );

  const displayMonthYear = `${filters?.month?.[0] ?? 'January'} ${currentYear}`;

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Staffing_Property_Count_${displayMonthYear}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};

export const downloadExcelStaffingRegionalKPI = async (
  staffingRegionData: StaffingRegionalKPITypes[],
  filters: ReportFilters,
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Staffing Regional Average');

  let displayMonth = '';
  const currentYear = filters?.year || '2025';

  if (filters?.month?.length) {
    if (filters.month?.length === 1) {
      displayMonth = filters?.month[0];
    } else {
      displayMonth =
        filters?.month[0] + ' - ' + filters?.month[filters?.month?.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyNameRowFont;
  worksheet.mergeCells('A1:J1');

  const titleRow = worksheet.addRow([`Regional Average Staffing KPI's `]);
  titleRow.font = reportTitleRowFont;
  worksheet.mergeCells('A2:J2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);
  periodRow.font = filterPeriodRowFont;
  worksheet.mergeCells('A3:J3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = filterConsolidationRowFont;
  worksheet.mergeCells('A4:J4');

  // creating group headings
  worksheet.addRow('');
  worksheet.addRow('');

  const groupHeadings = worksheet.addRow([
    '',
    'Period-End Actuals',
    '',
    '',
    '',
  ]);

  groupHeadings.eachCell((cell, colNum) => {
    if (colNum !== 1) {
      cell.font = headingFillMergeFont;
      cell.fill = headingFill;
      cell.alignment = alignmentCenter;
      cell.border = mainHeadingCellBorders;
    }
  });
  worksheet.mergeCells(
    `B${worksheet.lastRow?.number}:E${worksheet.lastRow?.number}`,
  );

  const subHeadingsRow = ['', 'Central', 'East', 'West', 'Consol.'];
  // worksheet.columns = columsWidth;
  const subheadings = worksheet.addRow(subHeadingsRow);
  subheadings.height = rowColumnHeadingHeight;

  worksheet.columns = [
    { width: 15 }, // label

    { width: 13 }, // Central
    { width: 13 }, // East
    { width: 13 }, // West
    { width: 13 }, // Consol.
  ];

  subheadings.eachCell((cell, colNum) => {
    if (colNum !== 1) {
      cell.fill = headingFill;
      cell.font = columnHeadingFillFont;
      cell.border = mainHeadingCellBorders;
      cell.alignment = alignmentCenter;
    }
  });

  tableRowOrderFormateStaffingRegional.forEach(({ label, key }, index) => {
    const item = staffingRegionData?.find(
      (item) => item?.parameter_name === key,
    );
    const rowCells = worksheet.addRow([
      label,
      item?.central_actual,
      item?.east_actual,
      item?.west_actual,
      item?.consolidated_actual,
    ]);

    rowCells.eachCell((cell, colNum) => {
      if (colNum !== 1) {
        cell.alignment = alignmentRight;
      } else {
        cell.alignment = alignmentLeft;
      }

      if (index % 2 === 0) {
        cell.fill = bodyRowCellFill;
        cell.border = bodyRowCellFullBorders;
      }
    });
  });

  const displayMonthYear = `${filters?.month?.[0] ?? 'January'} ${currentYear}`;

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Staffing_Regional_Average_${displayMonthYear}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};

export const downloadExcelStaffingMarketKPI = async (
  marketData: StaffingMarketKPITypes[],
  filters: ReportFilters,
) => {
  const workbook = new ExcelJS.Workbook();
  const worksheet = workbook.addWorksheet('Market Level Staffing');

  let displayMonth = '';
  const currentYear = filters?.year || '2025';

  if (filters?.month?.length) {
    if (filters.month?.length === 1) {
      displayMonth = filters?.month[0];
    } else {
      displayMonth =
        filters?.month[0] + ' - ' + filters?.month[filters?.month?.length - 1];
    }
  } else {
    displayMonth = `January`;
  }

  const companyRow = worksheet.addRow(['Willow Bridge Property Company LLC']);
  companyRow.font = companyNameRowFont;
  worksheet.mergeCells('A1:J1');

  const titleRow = worksheet.addRow([`Market-Level Staffing KPI's`]);
  titleRow.font = reportTitleRowFont;
  worksheet.mergeCells('A2:J2');

  const periodRow = worksheet.addRow([
    `For the Period Ending ${displayMonth} ${currentYear}`,
  ]);
  periodRow.font = filterPeriodRowFont;
  worksheet.mergeCells('A3:J3');

  const consolidationRow = worksheet.addRow([getConsolidationHeader(filters)]);
  consolidationRow.font = filterConsolidationRowFont;
  worksheet.mergeCells('A4:J4');

  // creating group headings
  worksheet.addRow('');
  worksheet.addRow('');
  const groupHeadings = worksheet.addRow([
    '',

    'Period-End Actuals',
    '',
    '',
    '',
    '',
    '',
  ]);
  groupHeadings.eachCell((cell, colNum) => {
    if (colNum !== 1) {
      cell.font = headingFillMergeFont;
      cell.fill = headingFill;
      cell.alignment = alignmentCenter;
      cell.border = mainHeadingCellBorders;
    }
  });
  worksheet.mergeCells(
    `B${worksheet.lastRow?.number}:G${worksheet.lastRow?.number}`,
  );

  const subHeadingsRow = [
    '',
    // Actual
    "RPM's",
    "VP's",
    'Properties',
    'Properties/ RPM.',
    'Workload/ RPM',
    "RPM's/ VP",
  ];
  // worksheet.columns = columsWidth;
  const subheadings = worksheet.addRow(subHeadingsRow);
  subheadings.height = rowColumnHeadingHeight;

  worksheet.columns = [
    { width: 13 },

    { width: 13 },
    { width: 13 },
    { width: 13 },
    { width: 13 },
    { width: 13 },
    { width: 13 },
  ];

  subheadings.eachCell((cell) => {
    cell.fill = headingFill;
    cell.font = columnHeadingFillFont;
    cell.border = mainHeadingCellBorders;
    cell.alignment = alignmentCenter;
  });

  const totals = {
    rpm_actual: marketData?.reduce(
      (sum, item) => sum + (item?.RPM_Actual ?? 0),
      0,
    ),
    vp_actual: marketData?.reduce(
      (sum, item) => sum + (item?.VP_Actual ?? 0),
      0,
    ),
    properties_actual: marketData?.reduce(
      (sum, item) => sum + (item?.Property_Actual ?? 0),
      0,
    ),
    properties_rpm_actual: marketData?.reduce(
      (sum, item) => sum + (item?.PropertyPerRPM_Actual ?? 0),
      0,
    ),
    workload_rpm_actual: marketData?.reduce(
      (sum, item) => sum + (item?.WorkloadPerRPM_Actual ?? 0),
      0,
    ),
    rpm_vp_actual: marketData?.reduce(
      (sum, item) => sum + (item?.RPMPerVP_Actual ?? 0),
      0,
    ),
  };

  marketData?.forEach((item, index) => {
    const rowCells = worksheet.addRow([
      item?.RegionMarket,
      item?.RPM_Actual,
      item?.VP_Actual,
      item?.Property_Actual,
      item?.PropertyPerRPM_Actual,
      item?.WorkloadPerRPM_Actual,
      item?.PropertyPerRPM_Actual,
    ]);

    rowCells.eachCell((cell, colNum) => {
      if (colNum !== 1) {
        cell.alignment = alignmentRight;
      } else {
        cell.alignment = alignmentLeft;
      }

      if (index % 2 === 0) {
        cell.fill = bodyRowCellFill;
        cell.border = bodyRowCellFullBorders;
      }

      if (colNum === 5 || colNum === 7) {
        cell.numFmt = '#,##0.0;(#,##0.0)';
      }
    });
  });

  const totalRow = worksheet.addRow([
    'Total',
    totals.rpm_actual,
    totals.vp_actual,
    totals.properties_actual,
    totals.properties_rpm_actual,
    totals.workload_rpm_actual,
    totals.rpm_vp_actual,
  ]);

  totalRow.eachCell((cell, colNum) => {
    cell.font = { bold: true, color: excelPurpleColor };
    cell.fill = {
      type: 'pattern',
      pattern: 'solid',
      // fgColor: lightBlueColor,
      fgColor: excelLightBlueColor,
    };
    if (colNum === 5 || colNum === 7) {
      cell.numFmt = '#,##0.0;(#,##0.0)';
    }
  });

  const displayMonthYear = `${filters?.month?.[0] ?? 'January'} ${currentYear}`;

  const buffer = await workbook.xlsx.writeBuffer();
  const blob = new Blob([buffer], {
    type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  });

  const url = window.URL.createObjectURL(blob);
  const a = document.createElement('a');
  a.href = url;
  a.download = `Staffing_Market_Level_${displayMonthYear}.xlsx`;
  a.click();
  window.URL.revokeObjectURL(url); // Clean up
};
