import { StaffingMarketKPITypes } from '@/api/staffingKpi/staffingKpiApi.types';
import { ReportFilters } from '@/slice/incomeReportSlice';
import { FileUp } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  CommonTable,
  CommonTableBodyCell,
  CommonTableBodyRow,
  CommonTableBodyRowTotal,
  CommonTableBodyTotalCell,
  CommonTableHeadingCell,
  CommonTableHeadingMergeCell,
  CommonTableMainHeaderRow,
  CommonTableSubHeaderRow,
} from '../../ReportsCommonComponents/commonReportsTableAtoms/CommonReportsTable';
import { downloadExcelStaffingMarketKPI } from '../utils/exportDownloadFormattersStaffingKPI';

interface PropsTypesMarketTable {
  marketTableData: StaffingMarketKPITypes[];
  datePeriod: string;
  filters: ReportFilters;
}

const staffingMarketColumnWidth = 'w-[100px]';

export default function MarketLevelStaffingTable(props: PropsTypesMarketTable) {
  const { marketTableData, datePeriod, filters } = props;

  const marketTotals = {
    rpm_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.RPM_Actual ?? 0),
      0,
    ),
    vp_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.VP_Actual ?? 0),
      0,
    ),
    properties_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.Property_Actual ?? 0),
      0,
    ),
    properties_rpm_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.PropertyPerRPM_Actual ?? 0),
      0,
    ),
    workload_rpm_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.WorkloadPerRPM_Actual ?? 0),
      0,
    ),
    rpm_vp_actual: marketTableData?.reduce(
      (sum, item) => sum + (item?.RPMPerVP_Actual ?? 0),
      0,
    ),
  };

  return (
    <div>
      <div className="mb-4 flex justify-between items-center">
        <h3 className="text-lg font-semibold text-[#43298F]">
          Period : {datePeriod}
        </h3>
        <Button
          variant="outline"
          className="bg-white text-[#43298F] border-[#43298F] hover:bg-[#DEEFFF]"
          onClick={() => {
            downloadExcelStaffingMarketKPI(marketTableData, filters);
          }}
        >
          <FileUp className="mr-2 h-4 w-4" />
          Export Excel
        </Button>
      </div>

      <CommonTable>
        <thead>
          <CommonTableMainHeaderRow>
            <th></th>
            <CommonTableHeadingMergeCell colSpan={6}>
              Period-End Actuals
            </CommonTableHeadingMergeCell>
          </CommonTableMainHeaderRow>

          <CommonTableSubHeaderRow>
            {/* <th className={`${staffingMarketColumnWidth} text-left`}></th> */}
            <th className={`w-[250px] text-left`}></th>
            <CommonTableHeadingCell
              className={`${staffingMarketColumnWidth}`}
              borderLeft
            >
              RPM's
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${staffingMarketColumnWidth}`}>
              VP's
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${staffingMarketColumnWidth}`}>
              Properties
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${staffingMarketColumnWidth}`}>
              Properties/ RPM
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${staffingMarketColumnWidth}`}>
              Workload/ RPM
            </CommonTableHeadingCell>
            <CommonTableHeadingCell className={`${staffingMarketColumnWidth}`}>
              RPM's/ VP
            </CommonTableHeadingCell>
          </CommonTableSubHeaderRow>
        </thead>

        <tbody>
          {marketTableData?.map((item) => {
            return (
              <CommonTableBodyRow key={item?.RegionMarket}>
                <td className="text-start px-1">{item?.RegionMarket}</td>
                {/* actuals */}
                <CommonTableBodyCell borderLeft={true}>
                  {item?.RPM_Actual}
                </CommonTableBodyCell>
                <CommonTableBodyCell>{item?.VP_Actual}</CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.Property_Actual}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.PropertyPerRPM_Actual?.toLocaleString('en-US', {maximumFractionDigits:1})}
                </CommonTableBodyCell>
                <CommonTableBodyCell>
                  {item?.WorkloadPerRPM_Actual}
                </CommonTableBodyCell>
                <CommonTableBodyCell borderRight={true}>
                  {item?.RPMPerVP_Actual?.toLocaleString('en-US', {maximumFractionDigits:1})}
                </CommonTableBodyCell>
              </CommonTableBodyRow>
            );
          })}
          <CommonTableBodyRowTotal>
            {/* <td className="text-start px-1 font-bold">Total</td> */}
            <CommonTableBodyTotalCell className="text-start ">
              TOTAL
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell borderLeft={true}>
              {marketTotals.rpm_actual?.toLocaleString('en-US', {maximumFractionDigits:1})}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.vp_actual?.toLocaleString('en-US', {maximumFractionDigits:1})}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.properties_actual?.toLocaleString('en-US', {maximumFractionDigits:1})}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.properties_rpm_actual?.toLocaleString('en-US', {maximumFractionDigits:1})}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell>
              {marketTotals.workload_rpm_actual?.toLocaleString('en-US', {maximumFractionDigits:1})}
            </CommonTableBodyTotalCell>

            <CommonTableBodyTotalCell borderRight={true}>
              {marketTotals.rpm_vp_actual?.toLocaleString('en-US', {maximumFractionDigits:1})}
            </CommonTableBodyTotalCell>
          </CommonTableBodyRowTotal>
        </tbody>
      </CommonTable>
    </div>
  );
}
