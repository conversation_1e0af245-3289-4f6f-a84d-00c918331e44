export const FONT_SIZES = {
  tiny: 'text-[10px]',

  xs: 'text-sm',

  sm: 'text-md',

  base: 'text-base',

  lg: 'text-lg',
} as const;

export const RESPONSIVE_FONTS = {
  // metricValue: 'text-xs sm:text-sm md:text-[11.5px] lg:text-base',
  metricValue: 'text-sm sm:text-base md:text-lg lg:text-xl',

  // metricLabel: 'text-xs sm:text-sm md:text-[11.5px]',
  metricLabel: 'text-xs sm:text-sm',

  // sectionHeader: 'text-sm sm:text-base',
  sectionHeader: 'text-base sm:text-lg',

  pageTitle: 'text-sm sm:text-base md:text-lg',

  // tableContent: 'text-xs',
  tableContent: 'text-sm',

  filterLabel: 'text-xs',

  buttonText: 'text-xs',

  // propertyDetails: 'text-xs',
  propertyDetails: 'text-sm',

  // summaryText: 'text-xs sm:text-sm md:text-[14px]',
  summaryText: 'text-sm sm:text-base',

  errorMessage: 'text-sm',

  loadingSkeleton: 'text-xs',
} as const;

export const LEGACY_FONT_MAPPING = {
  'text-xl': RESPONSIVE_FONTS.pageTitle,
  'text-lg': FONT_SIZES.sm,
  'text-base': FONT_SIZES.xs,
  'text-sm': FONT_SIZES.xs,
  'text-xs': FONT_SIZES.tiny,

  'text-lg sm:text-xl': RESPONSIVE_FONTS.pageTitle,
  'text-sm sm:text-base': RESPONSIVE_FONTS.sectionHeader,
  'text-sm sm:text-base lg:text-xl': RESPONSIVE_FONTS.metricValue,
  'text-xs sm:text-sm': RESPONSIVE_FONTS.metricLabel,
} as const;

export const CSS_FONT_SIZES = {
  tiny: '10px',
  xs: '12px',
  sm: '14px',
  base: '16px',
  lg: '18px',
} as const;

export const ICON_SIZES = {
  tiny: 'w-3 h-3',
  xs: 'w-4 h-4',
  sm: 'w-5 h-5',
  base: 'w-6 h-6',
  lg: 'w-7 h-7',
} as const;

export type FontSizeKey = keyof typeof FONT_SIZES;
export type ResponsiveFontKey = keyof typeof RESPONSIVE_FONTS;
