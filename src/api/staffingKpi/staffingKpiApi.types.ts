export interface StaffingKPIPayload {
  year: string | string[];
  month: string | string[];
  department: string | string[] | null;
  businessType: string | string[] | null;
  marketleader: string | string[] | null;
  adminBu: string | string[] | null;
}

export interface StaffingRPMKPITypes {
  // rpm_name: string | null;
  // expected_pm_fees: number | null;
  // lease_up: number | null;
  // new_construction: number | null;
  // stabilized: number | null;
  // total_properties: number | null;

  Expected_PM_Fees: number | null;
  Lease_Up: number | null;
  RPM: string | null;
  Stabilised: number | null;
  Total_Properties: number | null;
}

export interface StaffingRegionalKPITypes {
  // parameter_name: string | null;
  // central_actual: number | null;
  // east_actual: number | null;
  // west_actual: number | null;
  // consolidated_actual: number | null;
  // central_forecast: number | null;
  // east_forecast: number | null;
  // west_forecast: number | null;
  // consolidated_forecast: number | null;

  central_actual: number | null;
  consolidated_actual: number | null;
  east_actual: number | null;
  parameter_name: string | null;
  west_actual: number | null;
}

export interface StaffingMarketKPITypes {
  // market: string | null;
  // rpm_actual: number | null;
  // vp_actual: number | null;
  // properties_actual: number | null;
  // properties_rpm_actual: number | null;
  // workload_rpm_actual: number | null;
  // rpm_vp_actual: number | null;
  // rpm_forecast: number | null;
  // vp_forecast: number | null;
  // properties_forecast: number | null;
  // properties_rpm_forecast: number | null;
  // workload_rpm_forecast: number | null;
  // rpm_vp_forecast: number | null;

  PropertyPerRPM_Actual: number | null;
  Property_Actual: number | null;
  RPMPerVP_Actual: number | null;
  RPM_Actual: number | null;
  RegionMarket: string | null;
  VP_Actual: number | null;
  WorkloadPerRPM_Actual: number | null;
}
