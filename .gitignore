# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

node_modules
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
**/src/*.js

.env
.env.*
*.env
*.env.*
coverage/
build/
*.tsbuildinfo
*.log
*.lock
*.bak
*.tmp
*.cache
*.swp
*.swo
*.orig
*.rej
*.pid
*.seed
*.tgz
*.zip
*.tar
*.gz
*.rar
*.7z
*.sqlite
*.sqlite3
*.db
*.db3
*.sublime-workspace
*.sublime-project
*.iml
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
.idea/
.vscode/
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.DS_Store
Thumbs.db
*.mjs
*.mdc
.cursor/
*.mdc
TASKS.md
.cursor/
CLAUDE.md
.claude/ 
node_modules/
dist/
dist-ssr/
build/
coverage/
out/
.next/
.next/
.cache/
tmp/
temp/
**/src/*.js
